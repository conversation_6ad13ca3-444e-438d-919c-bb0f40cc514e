{"name": "framefuse", "version": "0.2.0", "private": true, "packageManager": "pnpm@9.0.0", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev --parallel", "dev:local": "bash scripts/dev-local.sh", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "typecheck": "turbo run typecheck", "format": "prettier --write .", "upload-ffz": "node scripts/upload-ffz.mjs", "get-session": "node scripts/get-session.mjs"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@vercel/blob": "^1.1.1", "fflate": "^0.8.2", "sharp": "^0.33.4"}, "devDependencies": {"@eslint/js": "^9.34.0", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "eslint": "^9.7.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.32.0", "js-yaml": "^4.1.0", "prettier": "^3.3.3", "turbo": "^2.0.6", "typescript": "^5.4.5", "vitest": "^1.6.0"}}